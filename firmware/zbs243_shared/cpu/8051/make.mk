FLAGS += -Icpu/8051

# Base optimization flags
FLAGS += -mmcs51 --std-c2x --opt-code-size --peep-file cpu/8051/peep.def --fomit-frame-pointer

# Advanced optimization flags for dead code elimination and size reduction
# Note: Removed --all-callee-saves to avoid conflicts with standard library
FLAGS += --no-xinit-opt --no-c-code-in-asm --no-peep-comments
FLAGS += --peep-return --allow-unsafe-read
FLAGS += --nostdlibcall --nooverlay
FLAGS += --max-allocs-per-node 50000
FLAGS += --peep-asm

SOURCES += cpu/8051/asmUtil.c
CC = sdcc
TARGETS	= main.ihx main.bin
OBJFILEEXT = rel


