ASxxxx Linker V03.00 + NoICE + sdld,  page 1.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
CABS2                               ********    ******** =           8. bytes (ABS,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   ********  l_CABS                          
C:   ********  l_GSINIT1                       
C:   ********  l_GSINIT5                       
C:   ********  l_IABS                          
C:   ********  l_ISEG                          
C:   ********  l_PSEG                          
C:   ********  l_REG_BANK_1                    
C:   ********  l_REG_BANK_2                    
C:   ********  l_REG_BANK_3                    
C:   ********  l_RSEG                          
C:   ********  l_RSEG0                         
C:   ********  l_RSEG1                         
C:   ********  l_XABS                          
C:   ********  l__CODE                         
C:   ********  s_BSEG                          
C:   ********  s_CABS                          
C:   ********  s_CABS2                         
C:   ********  s_DSEG                          
C:   ********  s_HOME                          
C:   ********  s_IABS                          
C:   ********  s_ISEG                          
C:   ********  s_REG_BANK_0                    
C:   ********  s_RSEG                          
C:   ********  s_RSEG0                         
C:   ********  s_RSEG1                         
C:   ********  s_XABS                          
C:   ********  l_BIT_BANK                      
C:   ********  l_GSFINAL                       
C:   ********  l_GSINIT0                       
C:   ********  l_BSEG_BYTES                    
C:   ********  l_OSEG                          
C:   ********  l_CABS2                         
C:   ********  l_REG_BANK_0                    
C:   ********  s_REG_BANK_1                    
C:   ********  l_GSINIT                        
C:   0000000A  l_GSINIT2                       
C:   ********  s_REG_BANK_2                    
C:   ********  s_REG_BANK_3                    
C:   0000001D  l_BSEG                          
C:   ********  s_BSEG_BYTES                    
C:   ********  s__CODE                         
C:   ********  l_GSINIT3                       
C:   ********  s_BIT_BANK                      
C:   0000002A  l_GSINIT4                       
C:   ********  s_OSEG                          
C:   0000003D  s_SSEG                          
C:   ********  l_HOME                          
C:   ********  l_DSEG                          
C:   000000AC  s_GSINIT0                       
C:   000000AF  s_GSINIT1                       
ASxxxx Linker V03.00 + NoICE + sdld,  page 2.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
CABS2                               ********    ******** =           8. bytes (ABS,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   000000AF  s_GSINIT2                       
C:   000000B9  s_GSINIT3                       
C:   000000C3  l_SSEG                          
C:   000000DB  s_GSINIT4                       
C:   00000100  l_IRAM                          
C:   00000105  s_GSINIT                        
C:   00000105  s_GSINIT5                       
C:   0000010E  s_GSFINAL                       
C:   00000111  s_CSEG                          
C:   000007FD  l_XSEG                          
C:   00000A19  l_CONST                         
C:   0000105B  l_XINIT                         
C:   0000105B  l_XISEG                         
C:   000055D8  l_CSEG                          
C:   000056E9  s_CONST                         
C:   00006102  s_XINIT                         
C:   0000E000  s_PSEG                          
C:   0000E000  s_XSEG                          
C:   0000E7FD  s_XISEG                         
ASxxxx Linker V03.00 + NoICE + sdld,  page 3.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
.  .ABS.                            ********    ******** =           0. bytes (ABS,CON)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
     ********  .__.ABS.                           _startup
     ********  _P0                                barcode
     ********  _P0_0                              barcode
     00000081  _P0_1                              barcode
     00000082  _DPL                               barcode
     00000082  _P0_2                              barcode
     00000083  _DPH                               barcode
     00000083  _P0_3                              barcode
     00000084  _DPL1                              barcode
     00000084  _P0_4                              barcode
     00000085  _DPH1                              barcode
     00000085  _P0_5                              barcode
     00000086  _P0_6                              barcode
     00000087  _P0_7                              barcode
     00000087  _PCON                              barcode
     00000088  _TCON                              barcode
     00000089  _TMOD                              barcode
     0000008A  _TL0                               barcode
     0000008B  _TL1                               barcode
     0000008C  _TH0                               barcode
     0000008D  _TH1                               barcode
     0000008E  _CLKSPEED                          barcode
     0000008F  _RESET                             barcode
     00000090  _P1                                barcode
     00000090  _P1_0                              barcode
     00000091  _I2CSTATE                          barcode
     00000091  _P1_1                              barcode
     00000092  _DPS                               barcode
     00000092  _P1_2                              barcode
     00000093  _P1_3                              barcode
     00000094  _I2CBUF                            barcode
     00000094  _P1_4                              barcode
     00000095  _I2CCTL                            barcode
     00000095  _P1_5                              barcode
     00000096  _I2CSPEED                          barcode
     00000096  _P1_6                              barcode
     00000097  _P1_7                              barcode
     00000098  _UARTSTA                           barcode
     00000098  _UART_RXF                          barcode
     00000099  _UARTBUF                           barcode
     00000099  _UART_TXE                          barcode
     0000009A  _UARTBRGL                          barcode
     0000009B  _UARTBRGH                          barcode
     000000A0  _P2                                barcode
     000000A0  _P2_0                              barcode
     000000A0  _XPAGE                             barcode
     000000A0  __XPAGE                            
     000000A1  _IEN1                              barcode
     000000A1  _P2_1                              barcode
     000000A2  _I2CUNKNOWN                        barcode
ASxxxx Linker V03.00 + NoICE + sdld,  page 4.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
.  .ABS.                            ********    ******** =           0. bytes (ABS,CON)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
     000000A2  _P2_2                              barcode
     000000A3  _P0LVLSEL                          barcode
     000000A3  _P2_3                              barcode
     000000A4  _P1LVLSEL                          barcode
     000000A4  _P2_4                              barcode
     000000A5  _P2LVLSEL                          barcode
     000000A5  _P2_5                              barcode
     000000A6  _P0INTEN                           barcode
     000000A6  _P2_6                              barcode
     000000A7  _P1INTEN                           barcode
     000000A7  _P2_7                              barcode
     000000A8  _IEN0                              barcode
     000000A8  _IEN_UART0                         barcode
     000000A9  _IEN_TMR0                          barcode
     000000A9  _P2INTEN                           barcode
     000000AA  _P0CHSTA                           barcode
     000000AB  _IEN_TMR1                          barcode
     000000AB  _P1CHSTA                           barcode
     000000AC  _IEN_RF1                           barcode
     000000AC  _P2CHSTA                           barcode
     000000AD  _IEN_RF2                           barcode
     000000AD  _P0FUNC                            barcode
     000000AE  _P1FUNC                            barcode
     000000AF  _IEN_EA                            barcode
     000000AF  _P2FUNC                            barcode
     000000B2  _PERFMON0                          barcode
     000000B3  _PERFMON1                          barcode
     000000B4  _PCH                               barcode
     000000B5  _PCL                               barcode
     000000B6  _PERFMON4                          barcode
     000000B7  _CLKEN                             barcode
     000000B9  _P0DIR                             barcode
     000000BA  _P1DIR                             barcode
     000000BA  _WDTENA                            barcode
     000000BB  _P2DIR                             barcode
     000000BB  _WDTPET                            barcode
     000000BC  _P0PULL                            barcode
     000000BC  _WDTRSTVALL                        barcode
     000000BD  _P1PULL                            barcode
     000000BD  _WDTRSTVALM                        barcode
     000000BE  _P2PULL                            barcode
     000000BE  _WDTRSTVALH                        barcode
     000000BF  _WDTCONF                           barcode
     000000C1  _UNK_C1                            barcode
     000000C7  _SETTINGS                          barcode
     000000C9  _RADIO_TXPTRL                      barcode
     000000CA  _RADIO_TXPTRH                      barcode
     000000CB  _RADIO_INITSEQ1                    barcode
     000000CC  _RADIO_INITSEQ2                    barcode
     000000CD  _RADIO_TXLEN                       barcode
     000000CE  _RADIO_INITSEQ0                    barcode
ASxxxx Linker V03.00 + NoICE + sdld,  page 5.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
.  .ABS.                            ********    ******** =           0. bytes (ABS,CON)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
     000000CF  _TCON2                             barcode
     000000D1  _RADIO_INITSEQ4                    barcode
     000000D2  _RADIO_INITSEQ5                    barcode
     000000D3  _RADIO_RXPTRL                      barcode
     000000D4  _RADIO_RXPTRH                      barcode
     000000D5  _RADIO_RXLEN                       barcode
     000000D6  _RADIO_INITSEQ3                    barcode
     000000D7  _TRIGGER                           barcode
     000000D8  _FPGNO                             barcode
     000000D9  _FWRSRCL                           barcode
     000000DA  _FWRSRCH                           barcode
     000000DB  _FWRDSTL                           barcode
     000000DC  _FWRDSTH                           barcode
     000000DD  _FWRLENL                           barcode
     000000DE  _FWRLENH                           barcode
     000000DF  _FWRTHREE                          barcode
     000000E0  _ACC                               barcode
     000000E6  _TEMPCAL1                          barcode
     000000E7  _TEMPCAL2                          barcode
     000000EB  _SPIUNKNOWN                        barcode
     000000EC  _SPICFG                            barcode
     000000ED  _SPIENA                            barcode
     000000EE  _SPITX                             barcode
     000000EF  _SPIRX                             barcode
     000000F0  _B                                 barcode
     000000F5  _B_5                               _gptrget
     000000F6  _B_6                               _gptrget
     000000F7  _B_7                               _gptrget
     000000F7  _TEMPCFG                           barcode
     000000F8  _TEMPRETH                          barcode
     000000F9  _TEMPRETL                          barcode
     000000FA  _RADIO_GOTLEN                      barcode
     000000FB  _TEMPCAL3                          barcode
     000000FC  _TEMPCAL4                          barcode
     000000FD  _TEMPCAL5                          barcode
     000000FE  _TEMPCAL6                          barcode
     000000FF  _CFGPAGE                           barcode
     00008C8A  _T0                                barcode
     00008D8B  _T1                                barcode

ASxxxx Linker V03.00 + NoICE + sdld,  page 6.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
BSEG_BYTES                          ********    ******** =           4. bytes (REL,CON)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 7.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
REG_BANK_0                          ********    ******** =           8. bytes (REL,OVR)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 8.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
BIT_BANK                            ********    ******** =           1. bytes (REL,OVR)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 9.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
DSEG                                ********    ******** =         128. bytes (REL,CON)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 10.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
OSEG                                ********    ******** =           5. bytes (REL,OVR)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 11.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
SSEG                                0000003D    000000C3 =         195. bytes (REL,OVR)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
     0000003D  __start__stack                     main

ASxxxx Linker V03.00 + NoICE + sdld,  page 12.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
XSEG                                0000E000    000007FD =        2045. bytes (REL,CON,XDATA)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
D:   0000E008  _mCurTemperature                   main
D:   0000E355  _commsTxUnencrypted_PARM_2         comms
D:   0000E3E4  _currentVer                        syncedproto
D:   0000E3EC  _blockId                           syncedproto
D:   0000E3ED  _mSelfMac                          syncedproto
D:   0000E46E  _avail                             syncedproto
D:   0000E625  _radioRxFilterCfg_PARM_2           radio
D:   0000E629  _radioRxFilterCfg_PARM_3           radio
D:   0000E62F  _radioRxDequeuePktGet_PARM_2       radio
D:   0000E631  _radioRxDequeuePktGet_PARM_3       radio
D:   0000E636  _flashWrite_PARM_2                 flash
D:   0000E638  _flashWrite_PARM_3                 flash
D:   0000E640  _flashRead_PARM_2                  flash
D:   0000E642  _flashRead_PARM_3                  flash
D:   0000E659  _rndSeed_PARM_2                    random
D:   0000E66F  _mScreenRow                        screen
D:   0000E7CB  __divulong_PARM_2                  _divulong
D:   0000E7D8  ___memcpy_PARM_2                   __memcpy
D:   0000E7DB  ___memcpy_PARM_3                   __memcpy
D:   0000E7E0  _memset_PARM_2                     _memset
D:   0000E7E1  _memset_PARM_3                     _memset
D:   0000E7E3  __gptrput_PARM_2                   _gptrput
D:   0000E7E4  __mulint_PARM_2                    _mulint
D:   0000E7E6  __mullong_PARM_2                   _mullong
D:   0000E7EA  _memcmp_PARM_2                     _memcmp
D:   0000E7ED  _memcmp_PARM_3                     _memcmp
D:   0000E7F2  __divsint_PARM_2                   _divsint
D:   0000E7F6  __divuint_PARM_2                   _divuint

ASxxxx Linker V03.00 + NoICE + sdld,  page 13.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
XISEG                               0000E7FD    0000105B =        4187. bytes (REL,CON,XDATA)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
D:   0000E7FD  _battery_voltage                   main
D:   0000E805  _dataPending                       syncedproto
D:   0000E806  _xferBuffer                        syncedproto
D:   0000F806  _curBlock                          syncedproto
D:   0000F815  _curDataInfo                       syncedproto
D:   0000F822  _curDataComplete                   syncedproto
D:   0000F823  _requestedParts                    syncedproto
D:   0000F829  _APmac                             syncedproto
D:   0000F831  _APsrcPan                          syncedproto
D:   0000F833  _isSynced                          syncedproto
D:   0000F834  _nextAttempt                       syncedproto
D:   0000F838  _isCalibrated                      syncedproto
D:   0000F839  _calib                             syncedproto
D:   0000F83B  _APburstInterval                   syncedproto
D:   0000F83F  _APburstLengthMs                   syncedproto
D:   0000F841  _APburstLength                     syncedproto
D:   0000F842  _seq                               syncedproto
D:   0000F843  _failedCheckins                    syncedproto
ASxxxx Linker V03.00 + NoICE + sdld,  page 14.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
HOME                                ********    ******** =          89. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   00000056  __sdcc_program_startup             main

ASxxxx Linker V03.00 + NoICE + sdld,  page 15.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
GSINIT0                             000000AC    ******** =           3. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   000000AC  __sdcc_gsinit_startup              

ASxxxx Linker V03.00 + NoICE + sdld,  page 16.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
GSINIT2                             000000AF    0000000A =          10. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 17.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
GSINIT3                             000000B9    ******** =          34. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   000000B9  __mcs51_genXINIT                   

ASxxxx Linker V03.00 + NoICE + sdld,  page 18.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
GSINIT4                             000000DB    0000002A =          42. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   000000DB  __mcs51_genRAMCLEAR                
C:   000000E1  __mcs51_genXRAMCLEAR               

ASxxxx Linker V03.00 + NoICE + sdld,  page 19.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
GSINIT                              00000105    ******** =           9. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 20.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
GSFINAL                             0000010E    ******** =           3. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 21.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
CSEG                                00000111    000055D8 =       21976. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   00000111  _fwVerString                       main
C:   0000017A  _voltString                        main
C:   0000021E  _getVolt                           main
C:   0000023B  _macSmallString                    main
C:   000002DE  _macString                         main
C:   00000311  _main                              main
C:   00000314  _eepromGetSize                     eeprom
C:   00000338  _eepromReadStart                   eeprom
C:   00000360  _eepromRead                        eeprom
C:   000004CC  _eepromDeepPowerDown               eeprom
C:   0000054F  _eepromInit                        eeprom
C:   00000884  _eepromWrite                       eeprom
C:   000009A1  _eepromErase                       eeprom
C:   00000B41  _eepromOtpModeEnter                eeprom
C:   00000B47  _eepromOtpModeExit                 eeprom
C:   000010B6  _set_offline                       drawing
C:   000013D2  _drawImageAtAddress                drawing
C:   000014FB  _drawFullscreenMsg                 drawing
C:   00001823  _commsGetLastPacketLQI             comms
C:   0000182E  _commsGetLastPacketRSSI            comms
C:   00001839  _commsRxUnencrypted                comms
C:   0000189D  _commsTxUnencrypted                comms
C:   000018FF  _commsTxNoCpy                      comms
C:   00001988  _settingsPrvDoWrite                settings
C:   00001B70  _settingsRead                      settings
C:   00001E07  _settingsWrite                     settings
C:   00002068  _charsDrawString                   chars
C:   0000212D  _initRadio                         syncedproto
C:   00002159  _initAfterWake                     syncedproto
C:   00002183  _getNextAttemptDelay               syncedproto
C:   000021EC  _sendTimingInfoReq                 syncedproto
C:   00002270  _getTimingInfo                     syncedproto
C:   00002362  _doSleep                           syncedproto
C:   000023CE  _getCalibration                    syncedproto
C:   00002492  _getSleepTiming                    syncedproto
C:   0000256A  _getSyncFrame                      syncedproto
C:   000025D0  _killRadio                         syncedproto
C:   000025FB  _getPacketType                     syncedproto
C:   0000270F  _sendAvailDataReq                  syncedproto
C:   00002839  _getAvailDataInfo                  syncedproto
C:   000028FC  _sendBlockRequest                  syncedproto
C:   00002997  _performBlockRequest               syncedproto
C:   00002A8C  _sendXferComplete                  syncedproto
C:   00002A8D  _doDataDownload                    syncedproto
C:   00002AD9  _doResync                          syncedproto
C:   00002B8A  _mainProtocolLoop                  syncedproto
C:   00002F3F  _clockingAndIntsInit               soc
C:   00002F49  _wdtOn                             wdt
C:   00002F5F  _wdtOff                            wdt
C:   00002F75  _wdtPet                            wdt
ASxxxx Linker V03.00 + NoICE + sdld,  page 22.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
CSEG                                00000111    000055D8 =       21976. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   00002F85  _wdtSetResetVal                    wdt
C:   00002FD0  _wdtDeviceReset                    wdt
C:   00002FE4  _sleepForMsec                      sleep
C:   00003179  _spiInit                           spi
C:   0000318C  _spiByte                           spi
C:   000031C0  _uartInit                          uart
C:   000031CD  _uartTx                            uart
C:   000031E2  _T0_ISR                            timer
C:   00003214  _timerGet                          timer
C:   00003274  _timerGetLowBits                   timer
C:   00003278  _timerInit                         timer
C:   00003292  _timerDelay                        timer
C:   000032EA  _RF_IRQ1                           radio
C:   000033CB  _RF_IRQ2                           radio
C:   00003474  _radioTx                           radio
C:   000034E3  _radioRxAckReset                   radio
C:   000034E6  _radioRxAckGetLast                 radio
C:   00003502  _radioRxFilterCfg                  radio
C:   0000357D  _radioRxEnable                     radio
C:   0000359A  _radioSetTxPower                   radio
C:   00003607  _radioSetChannel                   radio
C:   0000365A  _radioRxFlush                      radio
C:   00003661  _radioRxDequeuePktGet              radio
C:   000036E3  _radioRxDequeuedPktRelease         radio
C:   00003705  _radioInit                         radio
C:   00003941  _flashWrite                        flash
C:   00003A14  _flashRead                         flash
C:   00003ADA  _flashErase                        flash
C:   00003B85  _TEMP_ISR                          temperature
C:   00003BB0  _adcSampleTemperature              temperature
C:   00003E07  _rndSeed                           random
C:   00003FAD  _rndGen32                          random
C:   00003FB3  _rndGen8                           random
C:   00003FC0  _prvPrintFormat                    printf
C:   00004361  _pr                                printf
C:   000043CA  _spr                               printf
C:   00004413  _mathPrvU16from2xU8                asmUtil
C:   00004423  _mathPrvU32from4xU8                asmUtil
C:   00004437  _mathPrvU8bitswap                  asmUtil
C:   00004446  _mathPrvI16Asr1                    asmUtil
C:   00004453  _mathPrvMul8x8                     asmUtil
C:   00004468  _mathPrvMul16x8                    asmUtil
C:   0000448B  _mathPrvMul32x8                    asmUtil
C:   000044CA  _mathPrvMul16x16                   asmUtil
C:   00004536  _mathPrvSwapDptrR1R0               asmUtil
C:   0000453F  _u64_copy                          asmUtil
C:   00004555  _u64_copyFromCode                  asmUtil
C:   0000456C  _u64_add                           asmUtil
C:   00004587  _u64_and                           asmUtil
C:   000045A1  _u64_sub                           asmUtil
C:   000045BC  _u64_isLt                          asmUtil
ASxxxx Linker V03.00 + NoICE + sdld,  page 23.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
CSEG                                00000111    000055D8 =       21976. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
C:   000045D6  _u64_isEq                          asmUtil
C:   00004606  _u64_inc                           asmUtil
C:   0000460B  _u64_dec                           asmUtil
C:   00004611  _xMemSet                           asmUtil
C:   0000463D  _xMemEqual                         asmUtil
C:   0000466F  _xMemCopy                          asmUtil
C:   000046A9  _xMemCopyShort                     asmUtil
C:   000046D3  _xStrLen                           asmUtil
C:   000047D9  _mathPrvDiv32x16                   asmUtil
C:   000047DE  _mathPrvMod32x16                   asmUtil
C:   000047E3  _mathPrvDiv32x8                    asmUtil
C:   0000491F  _mathPrvDiv16x8                    asmUtil
C:   00004924  _mathPrvMod16x8                    asmUtil
C:   00004929  _charsPrvDerefAndIncGenericPtr     asmUtil
C:   00004960  _mathPrvCopyPostinc                asmUtil
C:   0000497C  _xMemEqual4                        asmUtil
C:   00004998  _u32minusU16                       asmUtil
C:   000049AD  _u32plusU16                        asmUtil
C:   000049C2  _u32Nonzero                        asmUtil
C:   000049CF  _i32Negative                       asmUtil
C:   000049D5  _powerPortsDownForSleep            board
C:   000049FA  _boardInit                         board
C:   00004A2D  _boardInitStage2                   board
C:   00004A2E  _boardGetOwnMac                    board
C:   00004AC6  _selfUpdate                        board
C:   00004EDA  _screenTxStart                     screen
C:   00004EED  _screenEndPass                     screen
C:   00004F06  _screenTxEnd                       screen
C:   00004F0C  _screenShutdown                    screen
C:   00004F18  _screenSleep                       screen
C:   00004F45  _screenByteTx                      screen
C:   00004FC2  _adcSampleBattery                  screen
C:   00005064  _barcodeIsDone                     barcode
C:   00005094  _barcodeNextBar                    barcode
C:   000052B4  __divulong                         _divulong
C:   000053A8  ___memcpy                          __memcpy
C:   0000541C  _memset                            _memset
C:   00005444  __gptrput                          _gptrput
C:   0000545F  __mulint                           _mulint
C:   0000547F  __mullong                          _mullong
C:   0000547F  __mullong_dummy                    _mullong
C:   000054EB  _memcmp                            _memcmp
C:   000055CA  __divsint                          _divsint
C:   0000563C  __divuint                          _divuint
C:   000056C9  __gptrget                          _gptrget
C:   000056E5  __sdcc_external_startup            _startup
ASxxxx Linker V03.00 + NoICE + sdld,  page 24.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
CONST                               000056E9    00000A19 =        2585. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 25.
Hexadecimal  [32-Bits]

Area                                    Addr        Size        Decimal Bytes (Attributes)
--------------------------------        ----        ----        ------- ----- ------------
XINIT                               00006102    0000105B =        4187. bytes (REL,CON,CODE)

      Value  Global                              Global Defined In Module
      -----  --------------------------------   ------------------------
ASxxxx Linker V03.00 + NoICE + sdld,  page 26.

Files Linked                              [ module(s) ]

main.rel                                  [  ]
eeprom.rel                                [  ]
drawing.rel                               [  ]
comms.rel                                 [  ]
settings.rel                              [  ]
chars.rel                                 [  ]
syncedproto.rel                           [  ]
soc/zbs243/soc.rel                        [  ]
soc/zbs243/wdt.rel                        [  ]
soc/zbs243/sleep.rel                      [  ]
soc/zbs243/spi.rel                        [  ]
soc/zbs243/uart.rel                       [  ]
soc/zbs243/timer.rel                      [  ]
soc/zbs243/radio.rel                      [  ]
soc/zbs243/flash.rel                      [  ]
soc/zbs243/temperature.rel                [  ]
cpu/8051/random.rel                       [  ]
cpu/8051/printf.rel                       [  ]
cpu/8051/asmUtil.rel                      [  ]
cpu/8051/cpu.rel                          [  ]
board/zbs154v033/board.rel                [  ]
board/zbs154v033/screen.rel               [  ]
barcode.rel                               [  ]


Libraries Linked                          [ object file ]

/usr/local/bin/../share/sdcc/lib/large/liblong.lib
                                          [ _divulong.rel ]
/usr/local/bin/../share/sdcc/lib/large/mcs51.lib
                                          [ crtclear.rel ]
/usr/local/bin/../share/sdcc/lib/large/libsdcc.lib
                                          [ __memcpy.rel ]
/usr/local/bin/../share/sdcc/lib/large/libsdcc.lib
                                          [ _memset.rel ]
/usr/local/bin/../share/sdcc/lib/large/mcs51.lib
                                          [ crtxinit.rel ]
/usr/local/bin/../share/sdcc/lib/large/libsdcc.lib
                                          [ _gptrput.rel ]
/usr/local/bin/../share/sdcc/lib/large/libint.lib
                                          [ _mulint.rel ]
/usr/local/bin/../share/sdcc/lib/large/liblong.lib
                                          [ _mullong.rel ]
/usr/local/bin/../share/sdcc/lib/large/libsdcc.lib
                                          [ _memcmp.rel ]
/usr/local/bin/../share/sdcc/lib/large/libint.lib
                                          [ _divsint.rel ]
/usr/local/bin/../share/sdcc/lib/large/mcs51.lib
                                          [ crtxclear.rel ]
/usr/local/bin/../share/sdcc/lib/large/libint.lib
                                          [ _divuint.rel ]
/usr/local/bin/../share/sdcc/lib/large/mcs51.lib
                                          [ crtpagesfr.rel ]
/usr/local/bin/../share/sdcc/lib/large/mcs51.lib
                                          [ crtstart.rel ]
/usr/local/bin/../share/sdcc/lib/large/libsdcc.lib
                                          [ _gptrget.rel ]
/usr/local/bin/../share/sdcc/lib/large/libsdcc.lib
                                          [ _startup.rel ]

ASxxxx Linker V03.00 + NoICE + sdld,  page 27.

User Base Address Definitions

HOME = 0x0000
XSEG = 0xe000
PSEG = 0xe000
ISEG = 0x0000
BSEG = 0x0000

