adcSampleTemperature
addAverageValue
addCapabilities
addCRC
addOverlay
aesCcmDec
aesCcmEnc
aesCcmPrvCalcUnencryptedMic
afterFlashScreenSaver
__at
barcodeIsDone
barcodeNextBar
blockRxLoop
checkButtonOr<PERSON>ig
checkCRC
commsGetLastPacketLQI
commsGetLastPacketRSSI
commsRxUnencrypted
commsTxNoCpy
commsTxUnencrypted
compareSettings
configEEPROM
configI2C
configSPI
configUART
continueToRX
detectAP
displayCustomImage
doSleep
doVoltageReading
downloadFWUpdate
downloadImageDataToEEPROM
drawImageAtAddress
drawImageFromEeprom
dump
eepromDeepPowerDown
eepromErase
eepromGetSize
eepromInit
eepromOtpModeEnter
eepromOtpModeExit
eepromPrvBusyWait
eepromPrvSfdpRead
eepromPrvSimpleCmd
eepromPrvWakeFromPowerdown
eepromRead
eepromReadStart
eepromWrite
eepromWriteLL
eraseImageBlock
eraseImageBlocks
eraseUpdateBlock
executeCommand
externalWakeHandler
findNextSlideshowImage
findSlotDataTypeArg
findSlotVer
getAddressForSlot
getAvailDataInfo
getDataBlock
getEepromImageDataArgument
getHighSlotId
getNextScanSleep
getNextSleep
getNumSlots
getPacketType
getShortAvailDataInfo
i2cBusScan
i2cCheckDevice
initializeProto
initPowerSaving
invalidateSettingsEEPROM
loadDefaultSettings
loadRawNTag
loadSettings
loadSettingsFromBuffer
loadURLtoNTag
main
md5Finalize
md5Init
md5Step
md5Update
pktIsUnicast
powerDown
powerUp
processAvailDataInfo
processBlockPart
processImageDataAvail
rotateLeft
saveImgBlockData
saveUpdateBlockData
sendAvailDataReq
sendBlockRequest
sendPing
sendShortAvailDataReq
sendTagReturnData
sendTagReturnDataPacket
sendXferComplete
sendXferCompletePacket
setupPortsInitial
showAPFound
showApplyUpdate
showFailedUpdate
showLongTermSleep
showNoAP
showNoEEPROM
showNoMAC
showSplashScreen
supportsNFCWake
swapEndian
upgradeSettings
validateBlockData
validateFWMagic
validateMD5
writeSettings
